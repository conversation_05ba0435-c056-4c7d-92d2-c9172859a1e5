/**
 * Scroll Management Utility
 * 
 * Provides utilities to manage page scrolling, especially for modal interactions
 * and resolving scroll conflicts between different components.
 */

/**
 * Force restore page scrolling
 * This function can be called to reset any stuck scroll states
 */
export function forceRestoreScrolling() {
  // Remove all modal-related classes
  document.body.classList.remove('modal-open');
  document.documentElement.classList.remove('modal-open');
  
  // Reset overflow styles
  document.body.style.overflow = "";
  document.documentElement.style.overflow = "";
  
  // Force a reflow to ensure changes take effect
  document.body.offsetHeight;
  
  console.log('Scroll state forcefully restored');
}

/**
 * Check if scrolling is currently disabled
 */
export function isScrollingDisabled() {
  const bodyOverflow = window.getComputedStyle(document.body).overflow;
  const documentOverflow = window.getComputedStyle(document.documentElement).overflow;
  const hasModalClass = document.body.classList.contains('modal-open') || 
                       document.documentElement.classList.contains('modal-open');
  
  return bodyOverflow === 'hidden' || documentOverflow === 'hidden' || hasModalClass;
}

/**
 * Debug scroll state - logs current scroll-related classes and styles
 */
export function debugScrollState() {
  console.log('=== Scroll State Debug ===');
  console.log('Body classes:', document.body.className);
  console.log('Document classes:', document.documentElement.className);
  console.log('Body overflow:', window.getComputedStyle(document.body).overflow);
  console.log('Document overflow:', window.getComputedStyle(document.documentElement).overflow);
  console.log('Body style overflow:', document.body.style.overflow);
  console.log('Document style overflow:', document.documentElement.style.overflow);
  console.log('Scrolling disabled:', isScrollingDisabled());
  console.log('========================');
}

/**
 * Auto-fix scroll state if it gets stuck
 * This can be called periodically or on specific events
 */
export function autoFixScrollState() {
  // Check if there are any visible modals
  const visibleModals = document.querySelectorAll('[role="dialog"]:not([style*="display: none"])');
  const hasVisibleModals = visibleModals.length > 0;
  
  // If no visible modals but scrolling is disabled, restore it
  if (!hasVisibleModals && isScrollingDisabled()) {
    console.warn('Detected stuck scroll state, auto-fixing...');
    forceRestoreScrolling();
  }
}

// Add a global event listener to auto-fix scroll issues
if (typeof window !== 'undefined') {
  // Auto-fix on route changes
  window.addEventListener('popstate', autoFixScrollState);

  // Auto-fix on focus (when user returns to tab)
  window.addEventListener('focus', () => {
    setTimeout(autoFixScrollState, 100);
  });

  // Add keyboard shortcut for debugging (Ctrl+Shift+S)
  window.addEventListener('keydown', (event) => {
    if (event.ctrlKey && event.shiftKey && event.key === 'S') {
      event.preventDefault();
      console.log('Manual scroll restore triggered');
      debugScrollState();
      forceRestoreScrolling();
    }
  });

  // Expose utilities globally for debugging
  window.scrollManager = {
    forceRestore: forceRestoreScrolling,
    debug: debugScrollState,
    autoFix: autoFixScrollState,
    isDisabled: isScrollingDisabled
  };

  console.log('Scroll Manager initialized. Use Ctrl+Shift+S to force restore scrolling or window.scrollManager for debugging.');
}
