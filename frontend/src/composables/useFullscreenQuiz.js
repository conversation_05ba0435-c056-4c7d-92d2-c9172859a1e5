/**
 * Composable for handling full-screen quiz sessions
 * Prevents user from exiting quiz and auto-submits on quit attempts
 */

import { ref } from "vue";
import { debug, warning, info } from "@/utils/logger";

export function useFullscreenQuiz(onQuizQuit) {
  const isFullscreen = ref(false);
  const quitAttempts = ref(0);
  const maxQuitAttempts = 1; // Changed to 1 for immediate submission
  const showQuitModal = ref(false);

  // Track various quit attempt methods
  const quitHandlers = {
    beforeunload: null,
    keydown: null,
    visibilitychange: null,
    blur: null,
    contextmenu: null,
    fullscreenchange: null,
  };

  const enterFullscreen = async () => {
    try {
      const element = document.documentElement;

      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen();
      }

      isFullscreen.value = true;
      info("Entered fullscreen mode for quiz");
      return true;
    } catch (error) {
      warning("Failed to enter fullscreen mode", { error });
      return false;
    }
  };

  const exitFullscreen = async () => {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen();
      }

      isFullscreen.value = false;
      info("Exited fullscreen mode");
    } catch (error) {
      warning("Failed to exit fullscreen mode", { error });
    }
  };

  const handleQuitAttempt = (source) => {
    quitAttempts.value++;
    warning(`Quiz quit attempt detected from ${source}`, {
      attempts: quitAttempts.value,
      maxAttempts: maxQuitAttempts,
    });

    // Show fullscreen modal and submit immediately
    showQuitModal.value = true;
    info("Quit attempt detected, submitting quiz immediately");
    
    // Submit quiz after showing modal
    setTimeout(() => {
      onQuizQuit?.();
    }, 2000);
    
    // Failsafe: Hide modal after maximum 10 seconds regardless of submission status
    setTimeout(() => {
      showQuitModal.value = false;
    }, 10000);
    
    return true;
  };

  const setupEventListeners = () => {
    // Prevent page unload
    quitHandlers.beforeunload = (event) => {
      event.preventDefault();
      event.returnValue = "";
      handleQuitAttempt("beforeunload");
      return "";
    };

    // Prevent common keyboard shortcuts
    quitHandlers.keydown = (event) => {
      const { key, ctrlKey, altKey, metaKey } = event;

      // Block common quit shortcuts
      if (
        (ctrlKey && (key === "w" || key === "W")) || // Ctrl+W
        (ctrlKey && (key === "t" || key === "T")) || // Ctrl+T
        (altKey && key === "F4") || // Alt+F4
        (metaKey && (key === "w" || key === "W")) || // Cmd+W
        key === "F11" || // F11 fullscreen toggle
        key === "Escape" // Escape key
      ) {
        event.preventDefault();
        event.stopPropagation();
        handleQuitAttempt("keyboard");
        return false;
      }
    };

    // Monitor page visibility changes
    quitHandlers.visibilitychange = () => {
      if (document.hidden) {
        handleQuitAttempt("visibility");
      }
    };

    // Monitor window blur (switching tabs/apps)
    quitHandlers.blur = () => {
      handleQuitAttempt("blur");
    };

    // Prevent right-click context menu
    quitHandlers.contextmenu = (event) => {
      event.preventDefault();
      return false;
    };

    // Monitor fullscreen changes
    quitHandlers.fullscreenchange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );

      if (!isCurrentlyFullscreen && isFullscreen.value) {
        handleQuitAttempt("fullscreen_exit");
        // No longer try to re-enter fullscreen since we submit immediately
      }
    };

    // Add all event listeners
    window.addEventListener("beforeunload", quitHandlers.beforeunload);
    document.addEventListener("keydown", quitHandlers.keydown, true);
    document.addEventListener(
      "visibilitychange",
      quitHandlers.visibilitychange,
    );
    window.addEventListener("blur", quitHandlers.blur);
    document.addEventListener("contextmenu", quitHandlers.contextmenu);
    document.addEventListener(
      "fullscreenchange",
      quitHandlers.fullscreenchange,
    );
    document.addEventListener(
      "webkitfullscreenchange",
      quitHandlers.fullscreenchange,
    );
    document.addEventListener(
      "msfullscreenchange",
      quitHandlers.fullscreenchange,
    );
  };

  const removeEventListeners = () => {
    window.removeEventListener("beforeunload", quitHandlers.beforeunload);
    document.removeEventListener("keydown", quitHandlers.keydown, true);
    document.removeEventListener(
      "visibilitychange",
      quitHandlers.visibilitychange,
    );
    window.removeEventListener("blur", quitHandlers.blur);
    document.removeEventListener("contextmenu", quitHandlers.contextmenu);
    document.removeEventListener(
      "fullscreenchange",
      quitHandlers.fullscreenchange,
    );
    document.removeEventListener(
      "webkitfullscreenchange",
      quitHandlers.fullscreenchange,
    );
    document.removeEventListener(
      "msfullscreenchange",
      quitHandlers.fullscreenchange,
    );
  };

  const initializeFullscreenQuiz = async () => {
    debug("Initializing fullscreen quiz mode");

    // Hide browser UI elements
    document.body.style.overflow = "hidden";
    document.body.style.userSelect = "none";
    document.documentElement.style.overflow = "hidden";

    // Setup event listeners
    setupEventListeners();

    // Enter fullscreen
    await enterFullscreen();

    info("Fullscreen quiz mode initialized");
  };

  const cleanupFullscreenQuiz = async () => {
    debug("Cleaning up fullscreen quiz mode");

    // Remove event listeners
    removeEventListeners();

    // Restore normal page behavior
    document.body.style.overflow = "";
    document.body.style.userSelect = "";
    document.documentElement.style.overflow = "";

    // Exit fullscreen
    if (isFullscreen.value) {
      await exitFullscreen();
    }

    info("Fullscreen quiz mode cleaned up");
  };

  const resetQuitAttempts = () => {
    quitAttempts.value = 0;
    debug("Reset quit attempts counter");
  };

  const closeQuitModal = () => {
    showQuitModal.value = false;
  };

  return {
    isFullscreen,
    quitAttempts,
    maxQuitAttempts,
    showQuitModal,
    closeQuitModal,
    initializeFullscreenQuiz,
    cleanupFullscreenQuiz,
    enterFullscreen,
    exitFullscreen,
    resetQuitAttempts,
  };
}
