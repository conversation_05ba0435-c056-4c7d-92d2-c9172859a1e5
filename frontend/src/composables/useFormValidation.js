/**
 * Form Validation Composable
 *
 * Provides reusable form validation with:
 * - Field validation rules
 * - Error state management
 * - Form submission handling
 * - Common validation patterns
 */
import { ref, reactive, computed } from "vue";

export function useFormValidation(initialData = {}, validationRules = {}) {
  // Form data
  const formData = reactive({ ...initialData });

  // Validation state
  const errors = ref({});
  const isValidating = ref(false);
  const hasInteracted = ref(false);
  const touchedFields = ref(new Set());

  /**
   * Common validation rules
   */
  const rules = {
    required: (value, fieldName = "Field") => {
      if (!value || (typeof value === "string" && !value.trim())) {
        return `${fieldName} is required`;
      }
      return null;
    },

    minLength:
      (min) =>
      (value, fieldName = "Field") => {
        if (value && value.length < min) {
          return `${fieldName} must be at least ${min} characters long`;
        }
        return null;
      },

    maxLength:
      (max) =>
      (value, fieldName = "Field") => {
        if (value && value.length > max) {
          return `${fieldName} must be less than ${max} characters`;
        }
        return null;
      },

    email: (value, fieldName = "Email") => {
      if (
        value &&
        !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)
      ) {
        return `${fieldName} must be a valid email address`;
      }
      return null;
    },

    username: (value, fieldName = "Username") => {
      if (value) {
        if (value.length < 2 || value.length > 50) {
          return `${fieldName} must be 2-50 characters long`;
        }
        if (!/^[a-zA-Z0-9._-]+$/.test(value)) {
          return `${fieldName} can only contain letters, numbers, underscore, dash, or dot`;
        }
      }
      return null;
    },

    arrayMinLength:
      (min) =>
      (value, fieldName = "Field") => {
        if (!Array.isArray(value) || value.length < min) {
          return `Please select at least ${min} ${fieldName.toLowerCase()}`;
        }
        return null;
      },

    custom: (validatorFn) => (value, fieldName) => {
      return validatorFn(value, fieldName);
    },
  };

  /**
   * Validate a single field
   */
  const validateField = (fieldName, value = formData[fieldName]) => {
    const fieldRules = validationRules[fieldName];
    if (!fieldRules) return null;

    // Ensure fieldRules is an array
    const rulesArray = Array.isArray(fieldRules) ? fieldRules : [fieldRules];

    for (const rule of rulesArray) {
      const error = rule(value, fieldName);
      if (error) {
        return error;
      }
    }
    return null;
  };

  /**
   * Validate all fields
   */
  const validateForm = () => {
    isValidating.value = true;
    const newErrors = {};

    Object.keys(validationRules).forEach((fieldName) => {
      const error = validateField(fieldName);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    errors.value = newErrors;
    isValidating.value = false;
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Mark field as touched and validate
   */
  const touchField = (fieldName) => {
    touchedFields.value.add(fieldName);
    hasInteracted.value = true;

    const error = validateField(fieldName);
    if (error) {
      errors.value[fieldName] = error;
    } else {
      delete errors.value[fieldName];
    }
  };

  /**
   * Clear errors for a specific field
   */
  const clearFieldError = (fieldName) => {
    delete errors.value[fieldName];
  };

  /**
   * Clear all errors
   */
  const clearErrors = () => {
    errors.value = {};
  };

  /**
   * Reset form to initial state
   */
  const resetForm = () => {
    Object.keys(formData).forEach((key) => {
      formData[key] = initialData[key] || "";
    });
    errors.value = {};
    hasInteracted.value = false;
    touchedFields.value.clear();
  };

  /**
   * Update form data
   */
  const updateField = (fieldName, value) => {
    formData[fieldName] = value;

    // Validate on change if field has been touched
    if (touchedFields.value.has(fieldName)) {
      touchField(fieldName);
    }
  };

  // Computed properties
  const isValid = computed(() => Object.keys(errors.value).length === 0);
  const hasErrors = computed(() => Object.keys(errors.value).length > 0);
  const errorCount = computed(() => Object.keys(errors.value).length);

  return {
    // Form data
    formData,

    // Validation state
    errors,
    isValidating,
    hasInteracted,
    touchedFields,

    // Computed
    isValid,
    hasErrors,
    errorCount,

    // Methods
    validateField,
    validateForm,
    touchField,
    clearFieldError,
    clearErrors,
    resetForm,
    updateField,

    // Validation rules
    rules,
  };
}

/**
 * Specialized form validation for assessment creation
 */
export function useAssessmentFormValidation() {
  const initialData = {
    assessmentName: "",
    assessmentDescription: "",
    selectedSkillIds: [],
    durationMinutes: 60,
    questionSelectionMode: "dynamic",
  };

  const validationRules = {
    assessmentName: [
      (value) => (!value?.trim() ? "Assessment name is required" : null),
      (value) =>
        value?.trim().length < 3
          ? "Assessment name must be at least 3 characters long"
          : null,
      (value) =>
        value?.trim().length > 255
          ? "Assessment name must be less than 255 characters"
          : null,
    ],
    assessmentDescription: [
      (value) => (!value?.trim() ? "Assessment description is required" : null),
    ],
    selectedSkillIds: [
      (value) =>
        !Array.isArray(value) || value.length === 0
          ? "Please select at least one skill"
          : null,
    ],
  };

  return useFormValidation(initialData, validationRules);
}

/**
 * Specialized form validation for skill creation
 */
export function useSkillFormValidation() {
  const initialData = {
    skillName: "",
    skillDescription: "",
  };

  const validationRules = {
    skillName: [
      (value) => (!value?.trim() ? "Skill name is required" : null),
      (value) =>
        value?.trim().length < 2
          ? "Skill name must be at least 2 characters long"
          : null,
    ],
    skillDescription: [
      (value) => (!value?.trim() ? "Skill description is required" : null),
    ],
  };

  return useFormValidation(initialData, validationRules);
}
