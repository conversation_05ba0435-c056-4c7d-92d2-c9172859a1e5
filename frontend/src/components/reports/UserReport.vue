<template>
  <div class="bg-gray-900/30 p-6 rounded-lg">
    <div class="space-y-6">
      <!-- Admin View: User List Header -->
      <div
        v-if="!selectedUser && !isEmployee"
        class="flex justify-between items-center"
      >
        <h3 class="text-lg font-semibold text-white">All Users</h3>
        <Button
          variant="generalAction"
          size="backButton"
          :disabled="usersLoading"
          class="bg-purple-600 hover:bg-purple-700"
          @click="fetchAllUsers"
        >
          {{ usersLoading ? "Loading..." : "Refresh Users" }}
        </Button>
      </div>

      <!-- Error/Success message -->
      <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
        <AlertDescription>{{ message }}</AlertDescription>
      </Alert>

      <!-- User List Table (Only visible to admins) -->
      <div v-if="!selectedUser && !isEmployee" class="overflow-x-auto">
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-gray-800/50 border-b border-gray-700">
              <th
                class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
              >
                User ID
              </th>
              <th
                class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
              >
                Name
              </th>
              <th
                class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
              >
                Email
              </th>
              <th
                class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
              >
                Assessments
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700">
            <tr
              v-for="user in users"
              :key="user.id"
              class="hover:bg-gray-700/30 transition-colors duration-150 cursor-pointer"
              title="Click to view user details"
              @click="selectUser(user.id)"
            >
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                {{ user.external_id || user.id || "Unknown ID" }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                {{
                  user.display_name ||
                  user.name ||
                  user.external_id ||
                  "Unknown Name"
                }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                {{ user.email || "N/A" }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                <span
                  :class="[
                    'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                    user.session_count > 0 || user.assessments > 0
                      ? 'bg-green-800/50 text-green-100'
                      : 'bg-gray-800/50 text-gray-300',
                  ]"
                >
                  {{ user.session_count || user.assessments || 0 }}
                </span>
              </td>
            </tr>
            <tr v-if="users.length === 0" class="cursor-default">
              <td colspan="4" class="px-4 py-8 text-center text-gray-400">
                <div v-if="usersLoading">Loading users...</div>
                <div v-else>
                  No users found. Click "Refresh Users" to load users.
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Loading indicator for employee view -->
      <div
        v-if="!selectedUser && isEmployee"
        class="flex justify-center items-center py-12"
      >
        <div
          class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"
        />
        <p class="ml-3 text-lg text-gray-300">Loading your report data...</p>
      </div>

      <!-- User Assessment Details -->
      <div v-if="selectedUser" class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-white">
            <!-- Change title for employee view -->
            <span v-if="isEmployee">Your Performance Report</span>
            <span v-else
              >User Details:
              {{
                userDetails?.display_name ||
                userDetails?.external_id ||
                "Loading..."
              }}</span
            >
          </h3>
          <Button
            v-if="!isEmployee"
            variant="userBack"
            size="backButton"
            @click="backToUserList"
          >
            <span class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to User List
            </span>
          </Button>
        </div>

        <div class="grid grid-cols-1 gap-4 mb-6">
          <div class="bg-gray-800/30 rounded-lg border border-purple-500/30">
            <!-- Spider Chart for Skill Performance -->
            <div class="bg-gray-800/30 rounded-lg p-5">
              <div class="flex justify-between items-center mb-2">
                <h4 class="text-md font-medium text-white">
                  Skill Performance Overview
                </h4>
                <button
                  v-if="
                    skillPerformanceData.length > 0 &&
                    skillPerformanceData[0].skill_name !==
                      'No Data Available' &&
                    skillPerformanceData[0].skill_name !== 'Error Loading Data'
                  "
                  class="text-xs text-purple-400 hover:text-purple-300 flex items-center"
                  @click="spiderChartModal.open()"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                  View Full Size
                </button>
              </div>

              <!-- No data message -->
              <div
                v-if="
                  skillPerformanceData.length === 0 ||
                  skillPerformanceData[0].skill_name === 'No Data Available' ||
                  skillPerformanceData[0].skill_name === 'Error Loading Data'
                "
                class="h-72 flex items-center justify-center"
              >
                <div class="text-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-16 w-16 mx-auto text-gray-600 mb-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                      d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <p class="text-gray-400 text-lg">
                    No skill performance data available
                  </p>
                  <p v-if="isEmployee" class="text-gray-500 mt-2">
                    Complete assessments to see your skill performance chart
                  </p>
                </div>
              </div>

              <!-- Spider Chart -->
              <div
                v-else
                class="h-72 cursor-pointer relative group"
                title="Click to view in full size"
                @click="spiderChartModal.open()"
              >
                <div
                  class="absolute inset-0 bg-purple-500/0 group-hover:bg-purple-500/10 transition-colors duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100 z-10"
                />
                <UserSkillsSpiderChart
                  :skills="skillPerformanceData"
                  color="#8b5cf6"
                  background-color="rgba(139, 92, 246, 0.2)"
                  :max-value="100"
                  :max-skills="6"
                />
              </div>
            </div>
          </div>

          <div
            class="bg-gray-800/30 rounded-lg border border-purple-500/30 shadow-lg hover:shadow-purple-500/10 transition-all duration-300"
          >
            <!-- Stats Summary -->
            <div class="rounded-lg p-6">
              <!-- Main Focus Alert - Prominent at the top -->
              <div
                v-if="getWorstSkill()?.accuracy_percentage < 60"
                class="bg-gradient-to-r from-red-900/40 to-red-700/20 border-l-4 border-red-500 rounded-lg p-4 mb-6 flex items-start"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 text-red-400 mr-3 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <div>
                  <h3 class="text-lg font-bold text-white">
                    Focus Required: {{ getWorstSkill()?.skill_name }}
                  </h3>
                  <p class="text-red-200 mt-1">
                    Your performance in
                    <span class="font-bold text-white">{{
                      getWorstSkill()?.skill_name
                    }}</span>
                    is at
                    <span class="font-bold text-red-300"
                      >{{ getWorstSkill()?.accuracy_percentage }}%</span
                    >, which requires immediate attention. We recommend
                    prioritizing this skill in your learning plan.
                  </p>
                  <div class="mt-3 flex space-x-2">
                    <div
                      class="bg-red-900/40 px-3 py-1 rounded-full text-xs text-red-200 font-medium"
                    >
                      Critical Skill Gap
                    </div>
                    <div
                      class="bg-red-900/40 px-3 py-1 rounded-full text-xs text-red-200 font-medium"
                    >
                      High Priority
                    </div>
                  </div>
                </div>
              </div>

              <h4
                class="text-lg font-semibold text-white mb-4 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-purple-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zm6-4a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zm6-3a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
                  />
                </svg>
                Performance Summary
              </h4>

              <!-- Main Stats -->
              <div class="grid grid-cols-2 gap-6 mb-6">
                <div
                  class="bg-gray-800/50 rounded-lg p-4 border border-purple-500/20 transform hover:scale-105 transition-transform duration-300"
                >
                  <p
                    class="text-sm text-purple-300 uppercase tracking-wider mb-1"
                  >
                    Overall Accuracy
                  </p>
                  <div class="flex items-end">
                    <p class="text-2xl font-bold text-white">
                      {{ getOverallAccuracy() }}%
                    </p>
                    <div
                      class="ml-2 h-6 w-16 bg-gray-700 rounded-full overflow-hidden"
                    >
                      <div
                        class="h-full bg-gradient-to-r from-purple-500 to-purple-300"
                        :style="`width: ${Math.min(getOverallAccuracy(), 100)}%`"
                      />
                    </div>
                  </div>
                </div>

                <div
                  class="bg-gray-800/50 rounded-lg p-4 border border-purple-500/20 transform hover:scale-105 transition-transform duration-300"
                >
                  <p
                    class="text-sm text-purple-300 uppercase tracking-wider mb-1"
                  >
                    Total Questions
                  </p>
                  <p class="text-2xl font-bold text-white">
                    {{ getTotalQuestions() }}
                  </p>
                </div>
              </div>

              <!-- Skill Performance Section -->
              <h6
                class="text-sm font-medium text-purple-300 uppercase tracking-wider mb-3 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
                Skill Performance
              </h6>

              <div class="grid grid-cols-2 gap-6 mb-6">
                <div
                  class="bg-gray-800/50 rounded-lg p-4 border border-green-500/20 transform hover:translate-y-[-2px] transition-transform duration-300"
                >
                  <div class="flex items-center mb-2">
                    <div class="w-2 h-2 rounded-full bg-green-400 mr-2" />
                    <p class="text-sm text-green-300 uppercase tracking-wider">
                      Best Skill
                    </p>
                  </div>
                  <p class="text-lg font-bold text-white mb-1 truncate">
                    {{ getBestSkill()?.skill_name || "N/A" }}
                  </p>
                  <div class="flex items-center">
                    <div class="w-full bg-gray-700 rounded-full h-2.5 mr-2">
                      <div
                        class="bg-gradient-to-r from-green-500 to-green-300 h-2.5 rounded-full"
                        :style="`width: ${getBestSkill()?.accuracy_percentage || 0}%`"
                      />
                    </div>
                    <p
                      class="text-sm font-medium text-green-400 whitespace-nowrap"
                    >
                      {{ getBestSkill()?.accuracy_percentage || 0 }}%
                    </p>
                  </div>
                </div>

                <div
                  class="bg-gray-800/50 rounded-lg p-4 border border-red-500/20 transform hover:translate-y-[-2px] transition-transform duration-300"
                >
                  <div class="flex items-center mb-2">
                    <div class="w-2 h-2 rounded-full bg-red-400 mr-2" />
                    <p class="text-sm text-red-300 uppercase tracking-wider">
                      Needs Improvement
                    </p>
                  </div>
                  <p class="text-lg font-bold text-white mb-1 truncate">
                    {{ getWorstSkill()?.skill_name || "N/A" }}
                  </p>
                  <div class="flex items-center">
                    <div class="w-full bg-gray-700 rounded-full h-2.5 mr-2">
                      <div
                        class="bg-gradient-to-r from-red-500 to-red-300 h-2.5 rounded-full"
                        :style="`width: ${getWorstSkill()?.accuracy_percentage || 0}%`"
                      />
                    </div>
                    <p
                      class="text-sm font-medium text-red-400 whitespace-nowrap"
                    >
                      {{ getWorstSkill()?.accuracy_percentage || 0 }}%
                    </p>
                  </div>
                </div>
              </div>

              <!-- Assessment Stats Section -->
              <h6
                class="text-sm font-medium text-purple-300 uppercase tracking-wider mb-3 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                Assessment Overview
              </h6>

              <div class="grid grid-cols-3 gap-4">
                <div
                  class="bg-gray-800/50 rounded-lg p-3 border border-purple-500/20 text-center"
                >
                  <p
                    class="text-xs text-purple-300 uppercase tracking-wider mb-1"
                  >
                    Total
                  </p>
                  <p class="text-xl font-bold text-white">
                    {{ userAssessments.length }}
                  </p>
                </div>

                <div
                  class="bg-gray-800/50 rounded-lg p-3 border border-green-500/20 text-center"
                >
                  <p
                    class="text-xs text-green-300 uppercase tracking-wider mb-1"
                  >
                    Completed
                  </p>
                  <p class="text-xl font-bold text-white">
                    {{
                      userAssessments.filter((a) => a.status === "completed")
                        .length
                    }}
                  </p>
                </div>

                <div
                  class="bg-gray-800/50 rounded-lg p-3 border border-yellow-500/20 text-center"
                >
                  <p
                    class="text-xs text-yellow-300 uppercase tracking-wider mb-1"
                  >
                    Pending
                  </p>
                  <p class="text-xl font-bold text-white">
                    {{
                      userAssessments.filter((a) => a.status === "pending")
                        .length
                    }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <h4 class="text-md font-medium text-white mt-6 mb-3">
          Assessment History
        </h4>
        <div class="overflow-x-auto bg-gray-800/30 rounded-lg p-4">
          <table class="w-full border-collapse">
            <thead>
              <tr class="bg-gray-700/70 border-b border-gray-700">
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Assessment
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Mode
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Score
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Easy
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Intermediate
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Advanced
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-700">
              <tr
                v-for="assessment in userAssessments"
                :key="assessment.session_id"
                class="hover:bg-gray-700/30 transition-colors duration-150"
              >
                <td class="px-4 py-3 text-sm text-gray-200">
                  {{ assessment.assessment_name }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                  {{ formatDate(assessment.mode) }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <span
                    :class="[
                      'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                      assessment.status === 'completed'
                        ? 'bg-green-800/50 text-green-100'
                        : 'bg-yellow-800/50 text-yellow-100',
                    ]"
                  >
                    {{
                      assessment.status === "completed"
                        ? "Completed"
                        : assessment.status === "pending"
                          ? "Pending"
                          : assessment.status
                    }}
                  </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <div
                    v-if="assessment.status === 'completed'"
                    class="flex items-center"
                  >
                    <span class="text-gray-200">{{
                      assessment.score || 0
                    }}</span>
                  </div>
                  <span v-else class="text-gray-400">-</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <span class="text-gray-200">{{
                    assessment.easy_count || 0
                  }}</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <span class="text-gray-200">{{
                    assessment.intermediate_count || 0
                  }}</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <span class="text-gray-200">{{
                    assessment.advanced_count || 0
                  }}</span>
                </td>
              </tr>
              <tr
                v-if="
                  userAssessments.length === 0 ||
                  userAssessments[0].status === 'no_data'
                "
              >
                <td colspan="7" class="px-4 py-8 text-center text-gray-400">
                  <div v-if="userAssessmentsLoading">
                    Loading assessment history...
                  </div>
                  <div v-else-if="isEmployee">
                    <p class="text-lg mb-2">
                      You haven't taken any assessments yet.
                    </p>
                    <p>
                      When you complete assessments, your results will appear
                      here.
                    </p>
                  </div>
                  <div v-else>No assessments found for this user.</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Skill History Section -->
        <div class="mt-8 space-y-4">
          <h4 class="text-md font-medium text-white mb-3">
            Skill Performance Data
          </h4>

          <!-- No data message -->
          <div
            v-if="
              skillPerformanceData.length === 0 ||
              (skillPerformanceData.length === 1 &&
                skillPerformanceData[0].skill_name === 'No Data Available')
            "
            class="bg-gray-800/30 rounded-lg p-6 text-center"
          >
            <div v-if="isEmployee" class="space-y-3">
              <p class="text-lg text-gray-300">
                No skill performance data available yet.
              </p>
              <p class="text-gray-400">
                Complete assessments to see your skill performance data here.
              </p>
            </div>
            <div v-else class="text-gray-400">
              No skill performance data available for this user.
            </div>
          </div>

          <!-- Comprehensive Skill Data Table -->
          <div
            v-if="
              skillPerformanceData.length > 0 &&
              skillPerformanceData[0].skill_name !== 'No Data Available' &&
              skillPerformanceData[0].skill_name !== 'Error Loading Data'
            "
            class="overflow-x-auto bg-gray-800/30 rounded-lg p-4"
          >
            <div class="overflow-x-auto">
              <table class="w-full text-sm text-left text-gray-300">
                <thead class="text-xs uppercase bg-gray-700/70 text-gray-300">
                  <tr>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Skill Name
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Questions
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Correct
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Accuracy %
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Total Score
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Avg Score
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Easy Correct
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Easy Incorrect
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Int. Correct
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Int. Incorrect
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Adv. Correct
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Adv. Incorrect
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="skill in skillPerformanceData"
                    :key="skill.skill_name"
                    class="border-b border-gray-700/50 hover:bg-gray-700/30 transition-colors duration-150"
                  >
                    <td class="px-3 py-2 font-medium text-white">
                      {{ skill.skill_name }}
                    </td>
                    <td class="px-3 py-2">
                      {{ skill.total_questions_answered }}
                    </td>
                    <td class="px-3 py-2">
                      {{ skill.correct_answers }}
                    </td>
                    <td class="px-3 py-2">
                      <span
                        :class="[
                          'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                          skill.accuracy_percentage >= 80
                            ? 'bg-green-800/50 text-green-100'
                            : skill.accuracy_percentage >= 60
                              ? 'bg-yellow-800/50 text-yellow-100'
                              : 'bg-red-800/50 text-red-100',
                        ]"
                      >
                        {{ skill.accuracy_percentage }}%
                      </span>
                    </td>
                    <td class="px-3 py-2">
                      {{ skill.total_score }}
                    </td>
                    <td class="px-3 py-2">
                      {{ skill.avg_score }}
                    </td>
                    <td class="px-3 py-2 text-green-400">
                      {{ skill.easy_correct || 0 }}
                    </td>
                    <td class="px-3 py-2 text-red-400">
                      {{ skill.easy_incorrect || 0 }}
                    </td>
                    <td class="px-3 py-2 text-green-400">
                      {{ skill.intermediate_correct || 0 }}
                    </td>
                    <td class="px-3 py-2 text-red-400">
                      {{ skill.intermediate_incorrect || 0 }}
                    </td>
                    <td class="px-3 py-2 text-green-400">
                      {{ skill.advanced_correct || 0 }}
                    </td>
                    <td class="px-3 py-2 text-red-400">
                      {{ skill.advanced_incorrect || 0 }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Spider Chart Modal -->
  <Dialog v-model="showSpiderChartModal" title="Skill Performance Overview">
    <div class="bg-gray-900 rounded-lg w-full mx-auto">
      <div class="h-96 w-full">
        <UserSkillsSpiderChart
          v-if="
            skillPerformanceData.length > 0 &&
            skillPerformanceData[0].skill_name !== 'No Data Available' &&
            skillPerformanceData[0].skill_name !== 'Error Loading Data'
          "
          :skills="skillPerformanceData"
          color="#8b5cf6"
          background-color="rgba(139, 92, 246, 0.2)"
          :max-value="100"
          :max-skills="6"
        />
        <div v-else class="h-full w-full flex items-center justify-center">
          <div class="text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-20 w-20 mx-auto text-gray-600 mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <p class="text-gray-400 text-xl">
              No skill performance data available
            </p>
            <p v-if="isEmployee" class="text-gray-500 mt-3">
              Complete assessments to see your skill performance chart.
              <br />Your progress will be tracked and displayed here.
            </p>
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog } from "@/components/ui/dialog";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { api } from "@/services/api";
import { UserSkillsSpiderChart } from "../charts";
import { debug, info, warning, error } from "@/utils/logger";
import { hasAdminAccess } from "@/utils/authHelpers";
import { useModal } from "@/composables";

const { message, isSuccess, setErrorMessage, clearMessage } =
  useMessageHandler();

// This will be populated with user skill performance data
const skillPerformanceData = ref([]);

// Helper functions for skill performance data
const getBestSkill = () => {
  if (!skillPerformanceData.value || skillPerformanceData.value.length === 0) {
    return null;
  }

  return [...skillPerformanceData.value].sort(
    (a, b) => (b.accuracy_percentage || 0) - (a.accuracy_percentage || 0),
  )[0];
};

const getWorstSkill = () => {
  if (!skillPerformanceData.value || skillPerformanceData.value.length === 0) {
    return null;
  }

  return [...skillPerformanceData.value].sort(
    (a, b) => (a.accuracy_percentage || 0) - (b.accuracy_percentage || 0),
  )[0];
};

const getOverallAccuracy = () => {
  if (!skillPerformanceData.value || skillPerformanceData.value.length === 0) {
    return 0;
  }

  const totalAccuracy = skillPerformanceData.value.reduce(
    (sum, skill) => sum + (skill.accuracy_percentage || 0),
    0,
  );

  return Math.round(totalAccuracy / skillPerformanceData.value.length);
};

const getTotalQuestions = () => {
  if (!skillPerformanceData.value || skillPerformanceData.value.length === 0) {
    return 0;
  }

  return skillPerformanceData.value.reduce(
    (sum, skill) => sum + (skill.total_questions_answered || 0),
    0,
  );
};

// Modal state using composable
const spiderChartModal = useModal();
const showSpiderChartModal = spiderChartModal.isOpen;

// All Users Tab Data
const users = ref([]);
const usersLoading = ref(false);
const selectedUser = ref(null);
const userDetails = ref(null);
const userAssessments = ref([]);
const userAssessmentsLoading = ref(false);

// Format date for display
const formatDate = (dateString) => {
  if (!dateString) return "N/A";

  // Handle special case for "dynamic" mode
  if (dateString === "dynamic") {
    return "Dynamic";
  }

  try {
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return dateString; // Return as is without logging
    }

    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch (error) {
    logError(error, "formatDate");

    return dateString; // Return as is without logging
  }
};

// Fetch all users
const fetchAllUsers = async () => {
  usersLoading.value = true;
  clearMessage();
  users.value = []; // Clear existing users

  try {
    const response = await api.admin.getUsers();
    const data = extractResponseData(response);

    // Check if the response has the expected structure
    if (data) {
      if (data.users && Array.isArray(data.users)) {
        users.value = data.users;

        if (users.value.length === 0) {
          setErrorMessage("No users found in the database.");
        }
      } else {
        setErrorMessage(
          "Invalid response format from server. Expected users array.",
        );

        // Try to extract users from a different format if possible
        if (typeof data === "object") {
          const possibleUsers = Object.values(data).find((val) =>
            Array.isArray(val),
          );
          if (possibleUsers && possibleUsers.length > 0) {
            users.value = possibleUsers;
          }
        }
      }
    } else {
      setErrorMessage("Invalid response from server");
    }
  } catch (error) {
    logError(error, "fetchAllUsers");
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(errorInfo.message || "Failed to fetch users");
    users.value = [];
  } finally {
    usersLoading.value = false;
  }
};

// Select a user to view details
const selectUser = async (userId) => {
  selectedUser.value = userId;
  userDetails.value = null;
  userAssessments.value = [];
  skillPerformanceData.value = [];
  userAssessmentsLoading.value = true;
  clearMessage();

  try {
    // Find the user in the local array first
    const localUser = users.value.find((u) => u.id === userId);

    // For employee view, we might not have the local user data yet
    // So we'll create a placeholder user object with the ID and email
    const userObj =
      localUser ||
      (isEmployee.value
        ? {
            id: userId,
            external_id: userId,
            name: "Current User",
            display_name: currentUserEmail.value
              ? `User (${currentUserEmail.value})`
              : "Current User",
            email: currentUserEmail.value || "N/A",
          }
        : null);

    if (!userObj) {
      setErrorMessage("User not found in local data");
      selectedUser.value = null;
      return;
    }

    debug("Using user object for data fetch:", { userObj });

    try {
      let response;

      // If we're in employee view and have an email but no valid numeric ID, use the email endpoint
      if (
        isEmployee.value &&
        currentUserEmail.value &&
        (isNaN(userId) || userId.toString().includes("ldap"))
      ) {
        debug(`Using email endpoint for user: ${currentUserEmail.value}`);
        response = await api.user.getUserAssessmentsByEmail(
          currentUserEmail.value,
        );
      } else {
        debug(`Fetching assessments for user ID: ${userId}`);
        response = await api.admin.getUserAssessments(userId);
      }

      debug("User assessments response:", { response });
      const data = extractResponseData(response);

      if (data) {
        // Set user details
        userDetails.value = {
          id: data.user_id || userObj.id,
          external_id: data.external_id || userObj.external_id || userObj.id,
          email: data.email || userObj.email || "N/A",
          display_name:
            data.display_name ||
            userObj.display_name ||
            userObj.name ||
            userObj.external_id ||
            "Unknown User",
        };

        // Set user assessments
        if (data.assessments && Array.isArray(data.assessments)) {
          if (data.assessments.length === 0) {
            info("No assessments found for user");
            // Create a placeholder assessment to show a message
            userAssessments.value = [
              {
                session_id: Date.now(),
                assessment_name: "No Assessments Found",
                mode: new Date().toISOString(),
                status: "no_data",
                score: 0,
                easy_count: 0,
                intermediate_count: 0,
                advanced_count: 0,
                session_created: new Date().toISOString(),
                session_completed: null,
                assessment_id: 0,
                assessment_description:
                  "You have not taken any assessments yet.",
                max_score: 0,
                percentage_score: 0,
              },
            ];
          } else {
            // Map the assessments data with default values for missing properties
            userAssessments.value = data.assessments.map((assessment) => {
              return {
                session_id: assessment.session_id || Date.now(),
                assessment_name:
                  assessment.assessment_name || "Unknown Assessment",
                mode:
                  assessment.mode ||
                  assessment.session_created ||
                  new Date().toISOString(),
                status: assessment.status || "unknown",
                score: parseInt(assessment.score || 0),
                easy_count: parseInt(assessment.easy_count || 0),
                intermediate_count: parseInt(
                  assessment.intermediate_count || 0,
                ),
                advanced_count: parseInt(assessment.advanced_count || 0),
                // Include any other properties that might be used in the template
                session_created: assessment.session_created,
                session_completed: assessment.session_completed,
                assessment_id: assessment.assessment_id,
                assessment_description: assessment.assessment_description,
                max_score: parseInt(assessment.max_score || 0),
                percentage_score: parseInt(assessment.percentage_score || 0),
              };
            });
          }
        } else {
          warning("No assessments array in response");
          userAssessments.value = [
            {
              session_id: Date.now(),
              assessment_name: "No Assessments Found",
              mode: new Date().toISOString(),
              status: "no_data",
              score: 0,
              easy_count: 0,
              intermediate_count: 0,
              advanced_count: 0,
              session_created: new Date().toISOString(),
              session_completed: null,
              assessment_id: 0,
              assessment_description: "No assessment data available.",
              max_score: 0,
              percentage_score: 0,
            },
          ];
        }

        // Fetch skill performance data from the separate endpoint
        try {
          let skillResponse;

          // If we're in employee view and have an email but no valid numeric ID, use the email endpoint
          if (
            isEmployee.value &&
            currentUserEmail.value &&
            (isNaN(userId) || userId.toString().includes("ldap"))
          ) {
            debug(
              `Using email endpoint for skill performance: ${currentUserEmail.value}`,
            );
            skillResponse = await api.user.getUserSkillPerformanceByEmail(
              currentUserEmail.value,
            );
          } else {
            debug(`Fetching skill performance for user ID: ${userId}`);
            skillResponse = await api.admin.getUserSkillPerformance(userId);
          }

          debug("Skill performance response:", { skillResponse });
          const skillData = extractResponseData(skillResponse);

          if (skillData) {
            if (Array.isArray(skillData)) {
              if (skillData.length > 0) {
                skillPerformanceData.value = skillData.map((skill) => {
                  // Create a skill object with default values and ensure all numeric values are parsed as numbers
                  return {
                    skill_name: skill.skill_name || "Unknown Skill",
                    total_questions_answered: parseInt(
                      skill.total_questions_answered || 0,
                    ),
                    correct_answers: parseInt(skill.correct_answers || 0),
                    accuracy_percentage: parseInt(
                      skill.accuracy_percentage || 0,
                    ),
                    total_score: parseInt(skill.total_score || 0),
                    avg_score: parseFloat(skill.avg_score || 0),
                    easy_correct: parseInt(skill.easy_correct || 0),
                    easy_incorrect: parseInt(skill.easy_incorrect || 0),
                    intermediate_correct: parseInt(
                      skill.intermediate_correct || 0,
                    ),
                    intermediate_incorrect: parseInt(
                      skill.intermediate_incorrect || 0,
                    ),
                    advanced_correct: parseInt(skill.advanced_correct || 0),
                    advanced_incorrect: parseInt(skill.advanced_incorrect || 0),
                  };
                });
              } else {
                info("No skill performance data found in array");
                // Create a placeholder skill data to show a message
                skillPerformanceData.value = [
                  {
                    skill_name: "No Data Available",
                    total_questions_answered: 0,
                    correct_answers: 0,
                    accuracy_percentage: 0,
                    total_score: 0,
                    avg_score: 0,
                    easy_correct: 0,
                    easy_incorrect: 0,
                    intermediate_correct: 0,
                    intermediate_incorrect: 0,
                    advanced_correct: 0,
                    advanced_incorrect: 0,
                  },
                ];
              }
            } else if (typeof skillData === "object") {
              // Handle case where response might be an object with a data property
              const skillDataArray =
                skillData.skills || skillData.skill_performance;

              if (Array.isArray(skillDataArray) && skillDataArray.length > 0) {
                skillPerformanceData.value = skillDataArray.map((skill) => ({
                  skill_name: skill.skill_name || "Unknown Skill",
                  total_questions_answered: parseInt(
                    skill.total_questions_answered || 0,
                  ),
                  correct_answers: parseInt(skill.correct_answers || 0),
                  accuracy_percentage: parseInt(skill.accuracy_percentage || 0),
                  total_score: parseInt(skill.total_score || 0),
                  avg_score: parseFloat(skill.avg_score || 0),
                  easy_correct: parseInt(skill.easy_correct || 0),
                  easy_incorrect: parseInt(skill.easy_incorrect || 0),
                  intermediate_correct: parseInt(
                    skill.intermediate_correct || 0,
                  ),
                  intermediate_incorrect: parseInt(
                    skill.intermediate_incorrect || 0,
                  ),
                  advanced_correct: parseInt(skill.advanced_correct || 0),
                  advanced_incorrect: parseInt(skill.advanced_incorrect || 0),
                }));
              } else {
                info("No skill performance data found in object");
                // Create a placeholder skill data to show a message
                skillPerformanceData.value = [
                  {
                    skill_name: "No Data Available",
                    total_questions_answered: 0,
                    correct_answers: 0,
                    accuracy_percentage: 0,
                    total_score: 0,
                    avg_score: 0,
                    easy_correct: 0,
                    easy_incorrect: 0,
                    intermediate_correct: 0,
                    intermediate_incorrect: 0,
                    advanced_correct: 0,
                    advanced_incorrect: 0,
                  },
                ];
              }
            } else {
              warning("Skill response data is not an array or object");
              // Create a placeholder skill data to show a message
              skillPerformanceData.value = [
                {
                  skill_name: "No Data Available",
                  total_questions_answered: 0,
                  correct_answers: 0,
                  accuracy_percentage: 0,
                  total_score: 0,
                  avg_score: 0,
                  easy_correct: 0,
                  easy_incorrect: 0,
                  intermediate_correct: 0,
                  intermediate_incorrect: 0,
                  advanced_correct: 0,
                  advanced_incorrect: 0,
                },
              ];
            }
          } else {
            info("No skill response data");
            // Create a placeholder skill data to show a message
            skillPerformanceData.value = [
              {
                skill_name: "No Data Available",
                total_questions_answered: 0,
                correct_answers: 0,
                accuracy_percentage: 0,
                total_score: 0,
                avg_score: 0,
                easy_correct: 0,
                easy_incorrect: 0,
                intermediate_correct: 0,
                intermediate_incorrect: 0,
                advanced_correct: 0,
                advanced_incorrect: 0,
              },
            ];
          }
        } catch (skillError) {
          error("Error fetching skill performance:", { error: skillError });
          logError(skillError, "getUserSkillPerformance");
          setErrorMessage(
            getErrorMessage(
              skillError,
              "Failed to fetch skill performance data",
            ),
          );

          // Create a placeholder skill data to show a message
          skillPerformanceData.value = [
            {
              skill_name: "Error Loading Data",
              total_questions_answered: 0,
              correct_answers: 0,
              accuracy_percentage: 0,
              total_score: 0,
              avg_score: 0,
              easy_correct: 0,
              easy_incorrect: 0,
              intermediate_correct: 0,
              intermediate_incorrect: 0,
              advanced_correct: 0,
              advanced_incorrect: 0,
            },
          ];
        }
      } else {
        setErrorMessage("Invalid response format from server");

        // Use local user data as fallback
        userDetails.value = {
          id: localUser.id,
          external_id: localUser.external_id || localUser.id,
          email: localUser.email || "N/A",
          display_name:
            localUser.display_name ||
            localUser.name ||
            localUser.external_id ||
            "Unknown User",
        };

        // Create a mock assessment as fallback
        userAssessments.value = [
          {
            session_id: Date.now(),
            session_code: "FALLBACK",
            session_created: new Date().toISOString(),
            session_completed: null,
            assessment_id: 1,
            assessment_name: "No Assessment Data Available",
            assessment_description:
              "Could not retrieve assessment data from server",
            score: 0,
            max_score: 0,
            percentage_score: 0,
            status: "in_progress",
          },
        ];
      }
    } catch (apiError) {
      error("API error:", { error: apiError });
      setErrorMessage(
        "Error fetching user assessment details. Using fallback data.",
      );

      // Use local user data as fallback
      userDetails.value = {
        id: userObj.id,
        external_id: userObj.external_id || userObj.id,
        email: userObj.email || "N/A",
        display_name:
          userObj.display_name ||
          userObj.name ||
          userObj.external_id ||
          "Unknown User",
      };

      // Create a mock assessment as fallback
      userAssessments.value = [
        {
          session_id: Date.now(),
          session_code: "ERROR",
          session_created: new Date().toISOString(),
          session_completed: null,
          assessment_id: 1,
          assessment_name: "Error Retrieving Assessments",
          assessment_description:
            "An error occurred while retrieving assessment data",
          score: 0,
          max_score: 0,
          percentage_score: 0,
          status: "in_progress",
        },
      ];
    }
  } catch (selectError) {
    error("Error selecting user:", { error: selectError });
    logError(selectError, "selectUser");
    setErrorMessage(
      getErrorMessage(selectError, "Failed to fetch user assessment details"),
    );

    // Create fallback user details
    userDetails.value = {
      id: userId,
      external_id: "Unknown",
      email: "N/A",
      display_name: "Unknown User",
    };
  } finally {
    userAssessmentsLoading.value = false;
  }
};

// Go back to user list
const backToUserList = () => {
  // If user is an employee, don't allow going back to user list
  if (isEmployee.value) {
    return;
  }

  selectedUser.value = null;
  userDetails.value = null;
  userAssessments.value = [];
  skillPerformanceData.value = [];
};

// Check if user is an employee (not admin)
const isEmployee = ref(false);
const currentUserId = ref(null);
const currentUserEmail = ref(null);

// Function to check user role and get current user email
const checkUserRole = () => {
  const userInfoStr = localStorage.getItem("user_info");
  if (!userInfoStr) return false;

  try {
    const userInfo = JSON.parse(userInfoStr);
    debug("User info from localStorage:", { userInfo });

    // Check if user has admin access using the new helper function
    const userHasAdminAccess = hasAdminAccess(userInfo);

    // Get employee group name from environment variables
    const employeeGroupName =
      import.meta.env.VITE_EMPLOYEE_GROUP_NAME || "employees";

    // Check if user has employee group but not admin access
    if (userInfo && userInfo.groups) {
      isEmployee.value =
        userInfo.groups.includes(employeeGroupName) && !userHasAdminAccess;

      // Store the current user email - we'll use this to find the user ID
      currentUserEmail.value = userInfo.email;

      // Check if user_info contains a user_id directly
      if (userInfo.user_id) {
        currentUserId.value = userInfo.user_id;
        debug(`Found user_id directly in user_info: ${currentUserId.value}`);
      } else if (userInfo.id) {
        currentUserId.value = userInfo.id;
        debug(`Found id directly in user_info: ${currentUserId.value}`);
      } else if (userInfo.sub) {
        currentUserId.value = userInfo.sub;
        debug(`Found sub directly in user_info: ${currentUserId.value}`);
      }

      // Log for debugging
      debug("User role check:", {
        isEmployee: isEmployee.value,
        userEmail: currentUserEmail.value,
        userId: currentUserId.value,
        groups: userInfo.groups,
      });

      return isEmployee.value;
    }
  } catch (parseError) {
    error("Error parsing user info:", { error: parseError });
  }

  return false;
};

// Function to find user ID by email
const findUserIdByEmail = async (email) => {
  if (!email) {
    error("No email provided to find user ID");
    return null;
  }

  debug(`Finding user ID for email: ${email}`);

  try {
    // Fetch all users first
    const response = await api.admin.getUsers();
    const data = extractResponseData(response);

    if (data) {
      let usersList = [];

      // Check if the response has the expected structure
      if (data.users && Array.isArray(data.users)) {
        usersList = data.users;
      } else if (typeof data === "object") {
        // Try to extract users from a different format if possible
        const possibleUsers = Object.values(data).find((val) =>
          Array.isArray(val),
        );
        if (possibleUsers && possibleUsers.length > 0) {
          usersList = possibleUsers;
        }
      }

      debug(`Found ${usersList.length} users, searching for email: ${email}`);

      // Find the user with the matching email (case-insensitive)
      let user = usersList.find(
        (u) =>
          u.email && email && u.email.toLowerCase() === email.toLowerCase(),
      );

      if (user) {
        debug(`Found user with matching email:`, { user });
        return user.id;
      } else {
        // Try to find by partial email match
        debug("Trying to find by partial email match");
        user = usersList.find(
          (u) =>
            u.email &&
            email &&
            (u.email.toLowerCase().includes(email.toLowerCase()) ||
              email.toLowerCase().includes(u.email.toLowerCase())),
        );

        if (user) {
          debug(`Found user with partial email match:`, { user });
          return user.id;
        }

        // Try to find by name or external_id that might contain the email
        debug("Trying to find by name or external_id");
        const emailUsername = email.split("@")[0];
        user = usersList.find(
          (u) =>
            (u.name &&
              u.name.toLowerCase().includes(emailUsername.toLowerCase())) ||
            (u.display_name &&
              u.display_name
                .toLowerCase()
                .includes(emailUsername.toLowerCase())) ||
            (u.external_id &&
              u.external_id
                .toLowerCase()
                .includes(emailUsername.toLowerCase())),
        );

        if (user) {
          debug(`Found user with name/id match:`, { user });
          return user.id;
        }

        // Log all users for debugging
        debug("All users:", {
          users: usersList.map((u) => ({
            id: u.id,
            email: u.email,
            name: u.name,
            display_name: u.display_name,
            external_id: u.external_id,
          })),
        });
        warning(`No user found with email: ${email}`);

        // As a last resort, use the first user in the list (for testing purposes)
        if (usersList.length > 0) {
          debug("Using first user as fallback:", { user: usersList[0] });
          return usersList[0].id;
        }

        return null;
      }
    } else {
      error("Invalid response from server when fetching users");
      return null;
    }
  } catch (findError) {
    error("Error finding user by email:", { error: findError });
    return null;
  }
};

// Initialize component
onMounted(async () => {
  // Check if user is an employee
  const isEmployeeOnly = checkUserRole();

  if (isEmployeeOnly) {
    if (currentUserId.value) {
      // If we already have the user ID, use it directly
      debug(`Employee detected with known user ID: ${currentUserId.value}`);
      selectUser(currentUserId.value);
    } else if (currentUserEmail.value) {
      // If we only have the email, find the user ID
      debug("Employee detected, finding user ID by email");
      const userId = await findUserIdByEmail(currentUserEmail.value);

      if (userId) {
        debug(`Found user ID: ${userId} for email: ${currentUserEmail.value}`);
        currentUserId.value = userId;
        // Fetch the user's data
        selectUser(userId);
      } else {
        error("Could not find user ID for the current user");
        setErrorMessage(
          "Could not find your user data. Please contact support.",
        );
      }
    } else {
      error("No user ID or email found for the current user");
      setErrorMessage(
        "Could not identify your user account. Please contact support.",
      );
    }
  } else {
    // Otherwise, fetch all users (admin view)
    fetchAllUsers();
  }
});
</script>
