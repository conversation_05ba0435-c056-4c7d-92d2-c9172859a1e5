<script setup>
import { ref } from "vue";
import { cn } from "@/lib/utils";

const props = defineProps({
  size: {
    type: Number,
    default: 28,
  },
  class: {
    type: String,
    default: "",
  },
});

const isAnimating = ref(false);
const isControlled = ref(false);

function startAnimation() {
  isControlled.value = true;
  isAnimating.value = true;
}

function stopAnimation() {
  isControlled.value = true;
  isAnimating.value = false;
}

function handleMouseEnter() {
  if (!isControlled.value) {
    isAnimating.value = true;
  }
}

function handleMouseLeave() {
  if (!isControlled.value) {
    isAnimating.value = false;
  }
}

defineExpose({
  startAnimation,
  stopAnimation,
});
</script>

<template>
  <div
    :class="
      cn(
        'cursor-pointer select-none flex items-center justify-center',
        props.class,
      )
    "
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      :width="size"
      :height="size"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="x-icon"
    >
      <path
        d="M18 6 6 18"
        class="x-path-1"
        :class="[isAnimating ? 'animate-path' : '']"
      />
      <path
        d="m6 6 12 12"
        class="x-path-2"
        :class="[isAnimating ? 'animate-path animate-path-delay' : '']"
      />
    </svg>
  </div>
</template>

<style scoped>
.animate-path {
  animation: path-animation 0.6s ease-in-out;
}

.animate-path-delay {
  animation-delay: 0.2s;
}

@keyframes path-animation {
  0% {
    opacity: 0;
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
  }
  100% {
    opacity: 1;
    stroke-dasharray: 100;
    stroke-dashoffset: 0;
  }
}

.x-icon {
  transition: filter 0.3s ease;
}

.x-icon:hover {
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.8));
}

.x-path-1,
.x-path-2 {
  stroke: white;
  stroke-width: 2.5;
}
</style>
