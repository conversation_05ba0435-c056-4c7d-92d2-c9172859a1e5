"""
Session Management Utility Functions

This module provides reusable session management utilities
to reduce code duplication and improve maintainability.
"""

from typing import Any, Dict, Optional

from fastapi import HTTPException, status

from .db_utils import get_single_row, get_single_value
from .hashid_utils import decode_session_code
from .logger import debug, error, warning


def validate_session_code_format(session_code_input: str) -> str:
    """
    Validate and normalize session code input.
    
    Args:
        session_code_input: Either a 6-digit session code or an encoded hash
    
    Returns:
        6-digit session code string
    
    Raises:
        HTTPException: If session code is invalid
    """
    debug(
        f"Validating session code: '{session_code_input}' "
        f"(type: {type(session_code_input)}, len: {len(session_code_input) if session_code_input else 'None'})"
    )
    
    # Handle None or empty string
    if not session_code_input:
        error("Empty session code provided")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Session code cannot be empty",
        )
    
    # Ensure session_code_input is a string
    session_code_input = str(session_code_input)
    
    # Check if it's already a 6-digit code
    if session_code_input.isdigit() and len(session_code_input) == 6:
        debug(f"Session code is already 6-digit: {session_code_input}")
        return session_code_input
    
    # Try to decode as hash
    decoded_code = decode_session_code(session_code_input)
    if decoded_code:
        debug(f"Decoded hash '{session_code_input}' to: {decoded_code}")
        return decoded_code
    
    # If neither worked, raise an error
    error(f"Failed to decode session code: '{session_code_input}'")
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail="Invalid session code format. Must be either a 6-digit code or valid hash.",
    )


def get_session_details(session_code: str) -> Optional[Dict[str, Any]]:
    """
    Get session details from database.
    
    Args:
        session_code: Session code to lookup
    
    Returns:
        Dictionary containing session details or None if not found
    """
    normalized_code = validate_session_code_format(session_code)
    
    query = """
        SELECT s.id as session_id, s.user_id, s.assessment_id, s.code as session_code,
               s.started_at as start_time, s.completed_at as end_time, s.status,
               s.created_at,
               a.name as assessment_name, a.description as assessment_description,
               a.question_selection_mode, a.duration_minutes as assessment_duration,
               u.external_id as user_external_id, u.display_name as user_display_name,
               u.email as user_email
        FROM sessions s
        LEFT JOIN assessments a ON s.assessment_id = a.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.code = %s
    """
    
    return get_single_row(query, (normalized_code,), as_dict=True)


def get_session_user_id(session_code: str) -> Optional[str]:
    """
    Get the external user ID for a session.
    
    Args:
        session_code: Session code to lookup
    
    Returns:
        External user ID or None if not found
    """
    query = """
        SELECT u.external_id
        FROM sessions s
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.code = %s
    """
    
    normalized_code = validate_session_code_format(session_code)
    return get_single_value(query, (normalized_code,))


def validate_session_ownership(session_code: str, user_id: str) -> bool:
    """
    Validate that a user owns a session.
    
    Args:
        session_code: Session code to validate
        user_id: User ID to check ownership
    
    Returns:
        True if user owns the session
    
    Raises:
        HTTPException: If validation fails
    """
    session_user_id = get_session_user_id(session_code)
    
    if not session_user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    if session_user_id != user_id:
        warning(f"Session ownership mismatch: session belongs to {session_user_id}, request from {user_id}")
        # Don't raise an exception here, just log the warning
        # The calling code can decide how to handle this
    
    return session_user_id == user_id


def get_session_remaining_time(session_code: str) -> int:
    """
    Calculate remaining time for a session in seconds.
    
    Args:
        session_code: Session code to check
    
    Returns:
        Remaining time in seconds
    """
    session_details = get_session_details(session_code)
    
    if not session_details:
        return 0
    
    # This is a simplified calculation - actual implementation would need
    # to consider elapsed time since session started.
    return session_details.get("remaining_time_seconds", 0)


def get_session_score(session_code: str) -> float:
    """
    Calculate current score for a session.
    
    Args:
        session_code: Session code to check
    
    Returns:
        Current score
    """
    session_details = get_session_details(session_code)
    
    if not session_details:
        return 0.0
    
    query = """
        SELECT SUM(score) as total_score
        FROM user_answers
        WHERE session_id = %s
    """
    
    result = get_single_value(query, (session_details["session_id"],))
    return float(result) if result else 0.0


def get_session_attempted_questions_count(session_code: str) -> int:
    """
    Get the number of attempted questions for a session.
    
    Args:
        session_code: Session code to check
    
    Returns:
        Number of attempted questions
    """
    session_details = get_session_details(session_code)
    
    if not session_details:
        return 0
    
    query = """
        SELECT COUNT(*)
        FROM user_answers
        WHERE session_id = %s
    """
    
    return get_single_value(query, (session_details["session_id"],), default=0)


def is_session_valid(session_code: str) -> bool:
    """
    Check if a session is valid and active.
    
    Args:
        session_code: Session code to validate
    
    Returns:
        True if session is valid
    """
    session_details = get_session_details(session_code)
    
    if not session_details:
        return False
    
    # Check if session is completed
    if session_details.get("is_completed", False):
        return False
    
    # Add more validation logic as needed
    return True


def get_session_questions_data(session_code: str) -> Dict[str, Any]:
    """
    Get comprehensive session and questions data.
    
    Args:
        session_code: Session code to lookup
    
    Returns:
        Dictionary containing session and question data
    """
    session_details = get_session_details(session_code)
    
    if not session_details:
        return {}
    
    return {
        "session_details": session_details,
        "current_score": get_session_score(session_code),
        "attempted_questions_count": get_session_attempted_questions_count(session_code),
        "remaining_time_seconds": get_session_remaining_time(session_code),
        "is_valid": is_session_valid(session_code),
    }


def update_session_status(session_code: str, updates: Dict[str, Any]) -> bool:
    """
    Update session status in database.
    
    Args:
        session_code: Session code to update
        updates: Dictionary of fields to update
    
    Returns:
        True if update was successful
    """
    from .db_utils import execute_query
    
    session_details = get_session_details(session_code)
    
    if not session_details:
        return False
    
    # Build update query
    set_clauses = []
    values = []
    
    for field, value in updates.items():
        set_clauses.append(f"{field} = %s")
        values.append(value)
    
    values.append(session_details["session_id"])
    
    query = f"""
        UPDATE sessions 
        SET {', '.join(set_clauses)}, updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
    """
    
    try:
        execute_query(query, tuple(values), commit=True)
        return True
    except Exception as e:
        error(f"Failed to update session status: {e}")
        return False